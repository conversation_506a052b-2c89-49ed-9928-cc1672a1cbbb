package exchange.common.dal.master.entity.cryptoToken;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import exchange.common.constant.CryptoToken;
import exchange.common.constant.CryptoTokenApplyStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * @author: wen.y
 * @date: 2024/10/21
 */
@Accessors(chain = true)
@Data
@TableName("crypto_token_apply")
public class CryptoTokenApplyDO implements Serializable {

	@Serial
	private static final long serialVersionUID = -6401992844500828365L;

	@TableId(value = "ID", type = IdType.AUTO)
	private Long id;

	@TableField("user_id")
	private Long userId;

	@TableField("crypto_token")
	private CryptoToken cryptoToken;

	@TableField("apply_status")
	private CryptoTokenApplyStatus applyStatus;

	@TableField("apply_time")
	private Date applyTime;

	@TableField("approved_time")
	private Date approvedTime;

	@TableField("created_at")
	private Date createdAt;

	@TableField("updated_at")
	private Date updatedAt;
}
