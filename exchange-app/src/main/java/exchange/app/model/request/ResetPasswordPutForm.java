package exchange.app.model.request;

import javax.validation.constraints.NotNull;
import exchange.common.model.request.IEmailForm;
import exchange.common.model.request.PasswordForm;
import lombok.Getter;
import lombok.Setter;

public class ResetPasswordPutForm extends PasswordForm implements IEmailForm, IMfaCodeForm {

  @Getter @Setter @NotNull private String email;

  @Getter @Setter @NotNull private String mfaCode;
}
