management:
  server:
    port: 9082
server:
  port: 9080
spring:
  data:
    redis:
      host: localhost
      port: 6379
  datasource:
    master:
      url: ***********************************************************************
      username: exchange
      password: Exchange123
    historical:
      url: *****************************************
      username: exchange
      password: Exchange123
exchange-pos:
  best-price:
    coinbook:
      api-host: http://localhost:8088
      api-url: /app/v1/ticker
exchange-websocket:
  redis-pubsub-cache:
    enabled: false # true: cache the message to avoid duplicate sending